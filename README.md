# Macroeconomic Event Trader - Multi-Agent AI Analysis System

A comprehensive financial analysis platform that uses multiple AI agents to automatically analyze market news and generate trading insights for any financial asset including forex pairs, commodities, cryptocurrencies, and stocks.

## 🚀 Features

### Multi-Asset Support
- **Forex Pairs**: EUR/USD, GBP/USD, USD/JPY, and all major currency pairs
- **Commodities**: Gold, Silver, Oil, Natural Gas, Agricultural products
- **Cryptocurrencies**: Bitcoin (BTC), Ethereum (ETH), and other digital assets
- **Stocks**: Apple (AAPL), Tesla (TSLA), NVIDIA (NVDA), and any publicly traded company
- **Market Indices**: S&P 500 (SPY), NASDAQ (QQQ), <PERSON> Jones (DIA)

### Multi-Agent AI System
The platform employs four specialized AI agents powered by Google's Gemini-2.5-pro:

1. **Bullish Researcher Agent**
   - Identifies positive market factors and growth opportunities
   - Analyzes supportive economic indicators
   - Provides optimistic market outlook

2. **Bearish Researcher Agent**
   - Identifies market risks and potential downturns
   - Analyzes cautionary signals and threats
   - Provides conservative risk assessment

3. **Risk Manager Agent**
   - Evaluates overall market risk levels
   - Assesses volatility and uncertainty factors
   - Provides risk mitigation strategies

4. **Trader Recommendation Agent**
   - Synthesizes insights from all agents
   - Generates actionable trading recommendations
   - Provides entry/exit strategies and position sizing

### Real-Time News Integration
- **RSS Feed Integration**: Pulls live news from 8+ professional financial sources
  - ForexLive - Real-time forex market updates
  - ActionForex - Professional forex analysis
  - Currency Thoughts - Expert currency insights
  - MarketWatch - Financial market news
  - Investing.com - Global financial data
- **Intelligent News Filtering**: Automatically filters relevant news based on asset type
- **Comprehensive Coverage**: Collects 9+ articles per analysis for thorough market assessment

### Advanced Analysis Features
- **Free-Text Asset Input**: Type any financial instrument for instant analysis
- **Autocomplete Suggestions**: Smart suggestions for popular assets
- **Multiple Time Ranges**: Last 24 hours, 3 days, 1 week analysis periods
- **Customizable Analysis Depth**: Standard and comprehensive analysis options
- **Real-Time Progress Tracking**: Live updates on analysis workflow status

## 🏗️ Architecture

### Frontend (React + TypeScript)
- **Modern UI**: Built with React, TypeScript, and Tailwind CSS
- **Real-Time Updates**: Live progress tracking with TanStack Query
- **Responsive Design**: Works seamlessly on desktop and mobile
- **Component Library**: Shadcn/ui components for consistent design

### Backend (Node.js + Express)
- **RESTful API**: Clean API endpoints for all functionality
- **PostgreSQL Database**: Persistent storage for analysis results
- **Real-Time Communication**: WebSocket support for live updates
- **Service Architecture**: Modular services for news, AI agents, and reporting

### Database Schema
```sql
-- Core analysis tracking
analyses: id, currency_pair, event_category, time_range, status, created_at

-- AI-generated reports from each agent
reports: id, analysis_id, agent_type, content, confidence_score

-- News articles collected for analysis
news_articles: id, analysis_id, title, description, source, published_at

-- Real-time agent status tracking
agent_statuses: id, analysis_id, agent_type, status, progress, message
```

## 🛠️ Technology Stack

### Core Technologies
- **Frontend**: React 18, TypeScript, Vite
- **Backend**: Node.js, Express, TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **AI Integration**: Google Gemini-2.5-pro API
- **Real-Time**: WebSocket connections

### Key Libraries
- **UI Components**: Shadcn/ui, Radix UI primitives
- **Styling**: Tailwind CSS with custom design system
- **State Management**: TanStack Query for server state
- **Form Handling**: React Hook Form with Zod validation
- **Database**: Drizzle ORM with type-safe queries
- **HTTP Client**: Fetch API with custom request handling

## 📦 Installation & Setup

### Prerequisites
- Node.js 18+ 
- PostgreSQL database
- Google Gemini API key

### Environment Variables
```bash
# Database Configuration
DATABASE_URL=postgresql://username:password@host:port/database

# AI Service API Keys
GEMINI_API_KEY=your_gemini_api_key_here

# Optional: News API (falls back to RSS feeds)
NEWS_API_KEY=your_news_api_key_here
```

### Quick Start
```bash
# Install dependencies
npm install

# Set up database
npm run db:push

# Start development server
npm run dev
```

The application will be available at `http://localhost:5000`

## 🎯 How to Use

### 1. Create New Analysis
1. Navigate to the dashboard
2. Enter any financial asset in the "Asset / Currency Pair" field
   - Examples: "EUR/USD", "Gold", "BTC", "AAPL", "Oil"
3. Select analysis parameters:
   - **Event Category**: All, Economic Data, Central Bank, Geopolitical
   - **Time Range**: Last 24 hours, 3 days, 1 week
   - **News Sources**: RSS Feeds, All Sources, specific providers
   - **Analysis Depth**: Standard or Comprehensive
4. Click "Start Analysis" to begin

### 2. Monitor Progress
- Watch real-time progress as agents work through the analysis
- Track news collection phase (typically 9+ articles)
- Monitor each AI agent's analysis progress
- View completion status and confidence scores

### 3. Review Results
The completed analysis includes:
- **Executive Summary**: Key findings and overall market sentiment
- **Bullish Analysis**: Positive factors and growth opportunities  
- **Bearish Analysis**: Risk factors and potential challenges
- **Risk Assessment**: Overall risk level and volatility analysis
- **Trading Recommendation**: Actionable insights and strategy
- **Source Articles**: All news articles used in the analysis

### 4. Export Results
- View detailed reports in organized tabs
- Access source articles with direct links
- Export analysis results (coming soon)

## 🔧 API Endpoints

### Analysis Management
```bash
# Create new analysis
POST /api/analysis
Content-Type: application/json
{
  "currencyPair": "EUR/USD",
  "eventCategory": "All", 
  "timeRange": "Last 24 hours",
  "newsSources": "RSS Feeds",
  "analysisDepth": "Standard"
}

# Get analysis results
GET /api/analysis/:id

# List all analyses
GET /api/analyses
```

### Real-Time Updates
```bash
# WebSocket connection for live updates
WS /ws/analysis/:id
```

## 🔍 Asset Types & Examples

### Currency Pairs
- Major pairs: EUR/USD, GBP/USD, USD/JPY
- Minor pairs: EUR/GBP, AUD/CAD, GBP/JPY
- Exotic pairs: USD/TRY, EUR/ZAR, USD/MXN

### Commodities
- **Precious Metals**: Gold, Silver, Platinum, Palladium
- **Energy**: Oil, Natural Gas, Gasoline, Heating Oil
- **Agriculture**: Wheat, Corn, Soybeans, Coffee, Sugar

### Cryptocurrencies
- **Major**: Bitcoin (BTC), Ethereum (ETH)
- **Altcoins**: Any cryptocurrency symbol
- **DeFi Tokens**: Supported through generic asset analysis

### Stocks & ETFs
- **Individual Stocks**: AAPL, TSLA, NVDA, MSFT, GOOGL
- **ETFs**: SPY, QQQ, DIA, XLF, XLE
- **International**: Any ticker symbol supported

## 📊 Analysis Output

Each analysis generates comprehensive reports including:

### Quantitative Metrics
- **Bullish Sentiment**: 0-100% confidence score
- **Bearish Sentiment**: 0-100% confidence score  
- **Risk Level**: Low, Medium, High classification
- **Overall Confidence**: Aggregate confidence score

### Qualitative Insights
- **Market Drivers**: Key factors moving the asset
- **Technical Levels**: Important support/resistance areas
- **Fundamental Analysis**: Economic and company-specific factors
- **Risk Factors**: Potential threats and uncertainties
- **Trading Strategy**: Specific recommendations and timing

## 🔒 Security & Privacy

- **API Key Management**: Secure environment variable storage
- **Database Security**: Encrypted connections and secure queries
- **Input Validation**: Comprehensive request validation with Zod
- **Error Handling**: Graceful error management and user feedback

## 🚀 Deployment

The application is designed for deployment on Replit with:
- **Automatic Scaling**: Handles multiple concurrent analyses
- **Database Persistence**: PostgreSQL with connection pooling
- **Environment Management**: Secure secret handling
- **Health Monitoring**: Built-in health checks and logging

## 📈 Performance

- **Analysis Speed**: Typically 30-60 seconds per complete analysis
- **News Collection**: 9+ articles per analysis from RSS feeds
- **Concurrent Users**: Supports multiple simultaneous analyses
- **Database Efficiency**: Optimized queries with proper indexing

## 🔄 Recent Updates

### v1.3.0 - Multi-Asset Support
- Added free-text asset input for any financial instrument
- Enhanced news filtering for commodities, crypto, and stocks
- Expanded asset term mapping for better relevance

### v1.2.0 - RSS Feed Integration  
- Integrated 8+ professional financial RSS feeds
- Increased news coverage from 3 to 9+ articles per analysis
- Added real-time news parsing and filtering

### v1.1.0 - Database Migration
- Migrated from in-memory to PostgreSQL storage
- Added persistent analysis history and results
- Implemented real-time agent status tracking

### v1.0.0 - Initial Release
- Multi-agent AI analysis system
- Gemini-2.5-pro integration  
- Real-time progress tracking
- Comprehensive reporting dashboard

## 🎯 Future Roadmap

- **Historical Analysis**: Backtesting and historical performance tracking
- **Portfolio Integration**: Multi-asset portfolio analysis
- **Alert System**: Automated notifications for significant market events
- **Advanced Charting**: Technical analysis with interactive charts
- **API Expansion**: Public API for third-party integrations

## 📞 Support

For technical support or feature requests, please refer to the project documentation or contact the development team.

---

**Built with modern web technologies and powered by advanced AI for comprehensive financial market analysis.**