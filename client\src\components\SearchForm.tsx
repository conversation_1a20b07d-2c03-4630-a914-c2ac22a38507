import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { z } from 'zod';
import { apiRequest } from '@/lib/queryClient';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Search, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const searchFormSchema = z.object({
  currencyPair: z.string().min(1, 'Currency pair is required'),
  eventCategory: z.string().min(1, 'Event category is required'),
  searchQuery: z.string().optional(),
  timeRange: z.string().min(1, 'Time range is required'),
  newsSources: z.string().min(1, 'News sources is required'),
  analysisDepth: z.string().min(1, 'Analysis depth is required'),
}).refine((data) => {
  // If event category is not "All", require search query
  if (data.eventCategory !== 'All' && (!data.searchQuery || data.searchQuery.trim() === '')) {
    return false;
  }
  return true;
}, {
  message: 'Search query is required for specific event categories',
  path: ['searchQuery'],
});

type SearchFormData = z.infer<typeof searchFormSchema>;

interface SearchFormProps {
  onAnalysisCreated: (analysisId: number) => void;
}

export default function SearchForm({ onAnalysisCreated }: SearchFormProps) {
  const { toast } = useToast();
  
  const form = useForm<SearchFormData>({
    resolver: zodResolver(searchFormSchema),
    defaultValues: {
      currencyPair: 'EUR/USD',
      eventCategory: 'Central Bank Policy',
      searchQuery: '',
      timeRange: 'Last week',
      newsSources: 'All Sources',
      analysisDepth: 'Standard',
    },
  });

  const createAnalysisMutation = useMutation({
    mutationFn: async (data: SearchFormData) => {
      const response = await apiRequest('POST', '/api/analysis', data);
      return await response.json();
    },
    onSuccess: (result) => {
      if (result.success) {
        toast({
          title: "Analysis Started",
          description: "Your forex analysis workflow has been initiated.",
        });
        onAnalysisCreated(result.analysisId);
      } else {
        throw new Error(result.message);
      }
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to start analysis",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: SearchFormData) => {
    createAnalysisMutation.mutate(data);
  };

  const handleClear = () => {
    form.reset();
  };

  return (
    <section className="mb-8">
      <Card className="bg-surface shadow-sm border border-gray-200">
        <CardContent className="p-6">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">Macroeconomic Event Analysis</h2>
          
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="block text-sm font-medium text-gray-700 mb-2">Asset / Currency Pair</Label>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Type any asset: EUR/USD, Gold, Oil, BTC, AAPL..."
                    value={form.watch('currencyPair')}
                    onChange={(e) => form.setValue('currencyPair', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    list="asset-suggestions"
                  />
                  <datalist id="asset-suggestions">
                    <option value="EUR/USD">EUR/USD - Euro vs US Dollar</option>
                    <option value="GBP/USD">GBP/USD - British Pound vs US Dollar</option>
                    <option value="USD/JPY">USD/JPY - US Dollar vs Japanese Yen</option>
                    <option value="USD/CHF">USD/CHF - US Dollar vs Swiss Franc</option>
                    <option value="AUD/USD">AUD/USD - Australian Dollar vs US Dollar</option>
                    <option value="USD/CAD">USD/CAD - US Dollar vs Canadian Dollar</option>
                    <option value="Gold">Gold - Precious Metal</option>
                    <option value="Silver">Silver - Precious Metal</option>
                    <option value="Oil">Oil - Crude Oil</option>
                    <option value="BTC">BTC - Bitcoin</option>
                    <option value="ETH">ETH - Ethereum</option>
                    <option value="SPY">SPY - S&P 500 ETF</option>
                    <option value="AAPL">AAPL - Apple Inc.</option>
                    <option value="TSLA">TSLA - Tesla Inc.</option>
                    <option value="NVDA">NVDA - NVIDIA Corp.</option>
                  </datalist>
                </div>
              </div>
              <div>
                <Label className="block text-sm font-medium text-gray-700 mb-2">Event Category</Label>
                <Select 
                  value={form.watch('eventCategory')} 
                  onValueChange={(value) => form.setValue('eventCategory', value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All (Comprehensive Analysis)</SelectItem>
                    <SelectItem value="Central Bank Policy">Central Bank Policy</SelectItem>
                    <SelectItem value="Employment Data">Employment Data</SelectItem>
                    <SelectItem value="Inflation Reports">Inflation Reports</SelectItem>
                    <SelectItem value="GDP Releases">GDP Releases</SelectItem>
                    <SelectItem value="Trade Balance">Trade Balance</SelectItem>
                    <SelectItem value="Political Events">Political Events</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label className="block text-sm font-medium text-gray-700 mb-2">
                Search Query
                {form.watch('eventCategory') === 'All' && (
                  <span className="text-xs text-gray-500 ml-2">(Optional - AI will analyze all economic factors)</span>
                )}
              </Label>
              <Input 
                {...form.register('searchQuery')}
                placeholder={
                  form.watch('eventCategory') === 'All' 
                    ? "Leave empty for comprehensive analysis of all economic factors"
                    : "e.g., Federal Reserve interest rate decision, ECB monetary policy"
                }
                className="w-full"
                disabled={form.watch('eventCategory') === 'All'}
              />
              {form.formState.errors.searchQuery && (
                <p className="text-sm text-error mt-1">{form.formState.errors.searchQuery.message}</p>
              )}
              {form.watch('eventCategory') === 'All' && (
                <p className="text-xs text-blue-600 mt-1">
                  AI agents will automatically research central bank policies, GDP, inflation, employment data, and other key economic indicators for {form.watch('currencyPair')}
                </p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label className="block text-sm font-medium text-gray-700 mb-2">Time Range</Label>
                <Select 
                  value={form.watch('timeRange')} 
                  onValueChange={(value) => form.setValue('timeRange', value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Last 24 hours">Last 24 hours</SelectItem>
                    <SelectItem value="Last 3 days">Last 3 days</SelectItem>
                    <SelectItem value="Last week">Last week</SelectItem>
                    <SelectItem value="Last month">Last month</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label className="block text-sm font-medium text-gray-700 mb-2">News Sources</Label>
                <Select 
                  value={form.watch('newsSources')} 
                  onValueChange={(value) => form.setValue('newsSources', value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All Sources">All Sources</SelectItem>
                    <SelectItem value="RSS Feeds">Forex RSS Feeds</SelectItem>
                    <SelectItem value="Reuters">Reuters</SelectItem>
                    <SelectItem value="Bloomberg">Bloomberg</SelectItem>
                    <SelectItem value="Financial Times">Financial Times</SelectItem>
                    <SelectItem value="MarketWatch">MarketWatch</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label className="block text-sm font-medium text-gray-700 mb-2">Analysis Depth</Label>
                <Select 
                  value={form.watch('analysisDepth')} 
                  onValueChange={(value) => form.setValue('analysisDepth', value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Quick Overview">Quick Overview</SelectItem>
                    <SelectItem value="Standard">Standard</SelectItem>
                    <SelectItem value="Deep Analysis">Deep Analysis</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <Button 
                type="button" 
                variant="outline" 
                onClick={handleClear}
                disabled={createAnalysisMutation.isPending}
              >
                Clear
              </Button>
              <Button 
                type="submit" 
                className="bg-primary text-white hover:bg-blue-700"
                disabled={createAnalysisMutation.isPending}
              >
                {createAnalysisMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Starting...
                  </>
                ) : (
                  <>
                    <Search className="mr-2 h-4 w-4" />
                    Start Analysis
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </section>
  );
}
