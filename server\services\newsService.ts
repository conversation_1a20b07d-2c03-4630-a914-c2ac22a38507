interface NewsApiResponse {
  status: string;
  totalResults: number;
  articles: Array<{
    source: { id: string | null; name: string };
    author: string | null;
    title: string;
    description: string;
    url: string;
    urlToImage: string | null;
    publishedAt: string;
    content: string;
  }>;
}

interface NewsSearchParams {
  query?: string;
  currencyPair?: string;
  timeRange: string;
  sources?: string;
  pageSize?: number;
}

export class NewsService {
  private apiKey: string;
  private baseUrl = 'https://newsapi.org/v2';
  private rssFeeds: string[] = [
    'https://www.forexlive.com/feed/news',
    'https://www.marketwatch.com/rss/realtimeheadlines',
    'https://www.actionforex.com/feed/',
    'https://www.investing.com/rss/news_301.rss',
    'https://feeds.feedburner.com/CurrencyThoughts',
    'https://www.investing.com/rss/news_14.rss',
    'https://www.myfxbook.com/rss/latest-forex-news',
    'https://www.forexlive.com/feed/centralbank'
  ];

  constructor() {
    this.apiKey = process.env.NEWS_API_KEY || '';
    if (!this.apiKey || this.apiKey.startsWith('AIza')) {
      console.log('Valid News API key not configured - using demonstration data');
      this.apiKey = '';
    }
  }

  async searchForexNews(params: NewsSearchParams): Promise<NewsApiResponse['articles']> {
    // Try RSS feeds for comprehensive forex coverage
    if (params.sources === 'RSS Feeds' || params.sources === 'All Sources') {
      try {
        const rssArticles = await this.fetchFromRssFeeds(params);
        if (rssArticles.length > 0) {
          return rssArticles;
        }
      } catch (error) {
        console.warn('RSS feeds failed:', error);
      }
    }

    if (!this.apiKey) {
      return this.getMockNewsData(params);
    }

    const { query, currencyPair, timeRange, sources, pageSize = 20 } = params;
    
    // Auto-generate comprehensive search query when none provided
    let forexQuery = query;
    if (!query || query.trim() === '') {
      forexQuery = this.generateComprehensiveQuery(currencyPair || 'EUR/USD');
    } else {
      forexQuery += ' AND (forex OR currency OR "foreign exchange" OR "central bank" OR "interest rate")';
    }
    
    // Convert timeRange to from date
    const fromDate = this.getFromDate(timeRange);
    
    const searchParams = new URLSearchParams({
      q: forexQuery,
      from: fromDate,
      sortBy: 'relevancy',
      pageSize: pageSize.toString(),
      apiKey: this.apiKey,
    });

    // Add sources if specified
    if (sources && sources !== 'All Sources') {
      const sourceMap: Record<string, string> = {
        'Reuters': 'reuters',
        'Bloomberg': 'bloomberg',
        'Financial Times': 'financial-times',
        'MarketWatch': 'marketwatch',
      };
      if (sourceMap[sources]) {
        searchParams.append('sources', sourceMap[sources]);
      }
    }

    try {
      const response = await fetch(`${this.baseUrl}/everything?${searchParams}`);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`News API error: ${response.status} - ${errorData.message || response.statusText}`);
      }

      const data: NewsApiResponse = await response.json();
      
      if (data.status !== 'ok') {
        throw new Error(`News API returned error status: ${data.status}`);
      }

      return data.articles.filter(article => 
        article.title && 
        article.content && 
        article.url &&
        !article.title.includes('[Removed]')
      );
    } catch (error) {
      console.error('Error fetching news:', error);
      throw new Error(`Failed to fetch news: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private getFromDate(timeRange: string): string {
    const now = new Date();
    let fromDate: Date;

    switch (timeRange) {
      case 'Last 24 hours':
        fromDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'Last 3 days':
        fromDate = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000);
        break;
      case 'Last week':
        fromDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'Last month':
        fromDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        fromDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    return fromDate.toISOString().split('T')[0];
  }

  private generateComprehensiveQuery(asset: string): string {
    // Handle currency pairs
    if (asset.includes('/')) {
      const [baseCurrency, quoteCurrency] = asset.split('/');
      const baseTerms = this.getCurrencyTerms(baseCurrency);
      const quoteTerms = this.getCurrencyTerms(quoteCurrency);
      
      const economicFactors = [
        'interest rates', 'inflation', 'GDP', 'employment', 'unemployment',
        'monetary policy', 'central bank', 'economic data', 'trade balance'
      ];
      
      const searchTerms = [
        ...baseTerms.slice(0, 2),
        ...quoteTerms.slice(0, 2),
        ...economicFactors.slice(0, 4)
      ];
      
      return searchTerms.join(' OR ');
    }
    
    // Handle commodities, crypto, and stocks
    return this.getAssetTerms(asset.toUpperCase()).join(' OR ');
  }

  private getCurrencyTerms(currency: string): string[] {
    const currencyMap: Record<string, string[]> = {
      'USD': ['Federal Reserve', 'Fed', 'US dollar', 'United States', 'Jerome Powell', 'FOMC', 'US economy'],
      'EUR': ['European Central Bank', 'ECB', 'euro', 'eurozone', 'Christine Lagarde', 'EU economy'],
      'GBP': ['Bank of England', 'BoE', 'pound sterling', 'UK', 'British pound', 'Andrew Bailey'],
      'JPY': ['Bank of Japan', 'BoJ', 'yen', 'Japan', 'Japanese economy', 'Kazuo Ueda'],
      'CHF': ['Swiss National Bank', 'SNB', 'Swiss franc', 'Switzerland', 'Swiss economy'],
      'AUD': ['Reserve Bank of Australia', 'RBA', 'Australian dollar', 'Australia', 'Michele Bullock'],
      'CAD': ['Bank of Canada', 'BoC', 'Canadian dollar', 'Canada', 'Tiff Macklem'],
      'NZD': ['Reserve Bank of New Zealand', 'RBNZ', 'New Zealand dollar', 'Adrian Orr']
    };
    
    return currencyMap[currency] || [`${currency} currency`, `${currency} economy`];
  }

  private getAssetTerms(asset: string): string[] {
    const assetMap: Record<string, string[]> = {
      // Precious Metals
      'GOLD': ['gold price', 'gold market', 'precious metals', 'gold futures', 'gold mining', 'inflation hedge'],
      'SILVER': ['silver price', 'silver market', 'precious metals', 'silver futures', 'industrial silver'],
      'PLATINUM': ['platinum price', 'platinum market', 'automotive industry', 'platinum mining'],
      
      // Energy
      'OIL': ['oil price', 'crude oil', 'petroleum', 'OPEC', 'energy market', 'oil production', 'oil demand'],
      'BRENT': ['Brent crude', 'oil price', 'petroleum', 'energy market', 'oil futures'],
      'WTI': ['WTI crude', 'oil price', 'petroleum', 'energy market', 'oil futures'],
      'GAS': ['natural gas', 'gas price', 'energy market', 'gas futures', 'LNG'],
      
      // Cryptocurrencies
      'BTC': ['Bitcoin', 'BTC', 'cryptocurrency', 'digital currency', 'crypto market', 'blockchain'],
      'BITCOIN': ['Bitcoin', 'BTC', 'cryptocurrency', 'digital currency', 'crypto market', 'blockchain'],
      'ETH': ['Ethereum', 'ETH', 'cryptocurrency', 'smart contracts', 'DeFi', 'crypto market'],
      'ETHEREUM': ['Ethereum', 'ETH', 'cryptocurrency', 'smart contracts', 'DeFi', 'crypto market'],
      
      // Major Stocks
      'AAPL': ['Apple', 'AAPL', 'iPhone', 'tech stocks', 'technology', 'Tim Cook'],
      'TSLA': ['Tesla', 'TSLA', 'electric vehicles', 'EV', 'Elon Musk', 'automotive'],
      'NVDA': ['NVIDIA', 'NVDA', 'semiconductors', 'AI chips', 'graphics cards', 'tech stocks'],
      'MSFT': ['Microsoft', 'MSFT', 'cloud computing', 'Azure', 'tech stocks', 'software'],
      'GOOGL': ['Google', 'Alphabet', 'GOOGL', 'tech stocks', 'advertising', 'search engine'],
      'AMZN': ['Amazon', 'AMZN', 'e-commerce', 'AWS', 'cloud computing', 'retail'],
      
      // Market Indices
      'SPY': ['S&P 500', 'SPY', 'US stocks', 'stock market', 'equity market', 'market index'],
      'QQQ': ['NASDAQ', 'QQQ', 'tech stocks', 'technology index', 'growth stocks'],
      'DIA': ['Dow Jones', 'DIA', 'industrial average', 'blue chip stocks', 'US market'],
      
      // Agriculture
      'WHEAT': ['wheat price', 'grain market', 'agriculture', 'food prices', 'farming'],
      'CORN': ['corn price', 'grain market', 'agriculture', 'biofuel', 'farming'],
      'SOYBEANS': ['soybean price', 'agriculture', 'grain market', 'China trade', 'farming']
    };

    // Return specific terms or generate generic ones
    if (assetMap[asset]) {
      return assetMap[asset];
    }

    // Generate generic terms for unknown assets
    return [
      `${asset} price`,
      `${asset} market`,
      `${asset} news`,
      `${asset} analysis`,
      `${asset} forecast`,
      `${asset} trading`
    ];
  }

  private async fetchFromRssFeeds(params: NewsSearchParams): Promise<NewsApiResponse['articles']> {
    console.log('Fetching from RSS feeds...');
    const articles: NewsApiResponse['articles'] = [];
    const maxFeeds = 5;
    
    for (let i = 0; i < Math.min(this.rssFeeds.length, maxFeeds); i++) {
      try {
        const feedUrl = this.rssFeeds[i];
        console.log(`Fetching RSS feed: ${feedUrl}`);
        
        const response = await fetch(feedUrl, {
          headers: { 
            'User-Agent': 'Mozilla/5.0 (compatible; ForexAnalyzer/1.0)',
            'Accept': 'application/rss+xml, application/xml, text/xml'
          },
          timeout: 15000
        });

        if (!response.ok) {
          console.warn(`RSS feed ${feedUrl} returned ${response.status}`);
          continue;
        }
        
        const xmlText = await response.text();
        const feedArticles = this.parseRssFeed(xmlText, feedUrl);
        console.log(`Parsed ${feedArticles.length} articles from ${feedUrl}`);
        
        articles.push(...feedArticles.slice(0, 3));
        
        if (articles.length >= 15) break;
      } catch (error) {
        console.warn(`RSS feed failed: ${this.rssFeeds[i]} - ${error}`);
      }
    }

    console.log(`Total RSS articles collected: ${articles.length}`);
    return articles.slice(0, params.pageSize || 20);
  }

  private parseRssFeed(xmlText: string, feedUrl: string): NewsApiResponse['articles'] {
    const articles: NewsApiResponse['articles'] = [];
    
    try {
      // Extract items using regex
      const itemRegex = /<item[^>]*>([\s\S]*?)<\/item>/gi;
      let match;
      
      while ((match = itemRegex.exec(xmlText)) !== null && articles.length < 5) {
        const itemContent = match[1];
        
        const title = this.extractCData(this.extractXmlTag(itemContent, 'title'));
        const description = this.extractCData(this.extractXmlTag(itemContent, 'description'));
        const link = this.extractXmlTag(itemContent, 'link') || this.extractXmlTag(itemContent, 'guid');
        const pubDate = this.extractXmlTag(itemContent, 'pubDate');
        
        if (title && description) {
          articles.push({
            source: { id: null, name: this.getSourceName(feedUrl) },
            author: this.extractCData(this.extractXmlTag(itemContent, 'dc:creator')) || null,
            title: this.cleanText(title),
            description: this.cleanText(description),
            url: link || feedUrl,
            urlToImage: null,
            publishedAt: pubDate ? new Date(pubDate).toISOString() : new Date().toISOString(),
            content: this.cleanText(description)
          });
        }
      }
    } catch (error) {
      console.warn(`RSS parsing error for ${feedUrl}:`, error);
    }
    
    return articles;
  }

  private extractXmlTag(xml: string, tag: string): string {
    const regex = new RegExp(`<${tag}[^>]*>([\\s\\S]*?)<\\/${tag}>`, 'i');
    const match = xml.match(regex);
    return match ? match[1].trim() : '';
  }

  private extractCData(text: string): string {
    const cdataMatch = text.match(/<!\[CDATA\[([\s\S]*?)\]\]>/);
    return cdataMatch ? cdataMatch[1] : text;
  }

  private cleanText(text: string): string {
    return text
      .replace(/<!\[CDATA\[(.*?)\]\]>/g, '$1')
      .replace(/<[^>]*>/g, '')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&nbsp;/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  }

  private getSourceName(feedUrl: string): string {
    const sourceMap: Record<string, string> = {
      'forexlive.com': 'ForexLive',
      'marketwatch.com': 'MarketWatch',
      'actionforex.com': 'ActionForex',
      'investing.com': 'Investing.com',
      'myfxbook.com': 'MyFxBook',
      'feedburner.com': 'Currency Thoughts'
    };
    
    for (const [domain, name] of Object.entries(sourceMap)) {
      if (feedUrl.includes(domain)) return name;
    }
    
    return 'RSS Feed';
  }

  private getMockNewsData(params: NewsSearchParams): NewsApiResponse['articles'] {
    const baseArticles = [
      {
        source: { id: 'reuters', name: 'Reuters' },
        author: 'Financial Reporter',
        title: 'Federal Reserve Signals Potential Interest Rate Adjustments Amid Economic Uncertainty',
        description: 'The Federal Reserve indicated potential monetary policy shifts as economic indicators show mixed signals.',
        url: 'https://example.com/fed-signals',
        urlToImage: null,
        publishedAt: new Date().toISOString(),
        content: 'Federal Reserve officials have suggested that interest rate adjustments may be necessary to address current economic conditions. Recent data shows inflation pressures alongside employment concerns, creating a complex policy environment for central bank decision-makers.'
      },
      {
        source: { id: 'bloomberg', name: 'Bloomberg' },
        author: 'Market Analyst',
        title: 'EUR/USD Rally Continues as ECB Maintains Hawkish Stance on Inflation',
        description: 'European Central Bank maintains aggressive approach to combat persistent inflation pressures.',
        url: 'https://example.com/ecb-hawkish',
        urlToImage: null,
        publishedAt: new Date(Date.now() - 3600000).toISOString(),
        content: 'The European Central Bank continues its hawkish monetary policy stance, with officials emphasizing the need to bring inflation back to target levels. This approach has strengthened the euro against major currencies, particularly the US dollar.'
      },
      {
        source: { id: 'financial-times', name: 'Financial Times' },
        author: 'Economics Editor',
        title: 'GDP Growth Forecasts Revised Downward Amid Global Trade Tensions',
        description: 'Major economies face headwinds as trade uncertainties impact growth projections.',
        url: 'https://example.com/gdp-revised',
        urlToImage: null,
        publishedAt: new Date(Date.now() - 7200000).toISOString(),
        content: 'Economic growth forecasts have been revised lower for major economies as ongoing trade tensions and supply chain disruptions continue to weigh on business confidence and investment decisions.'
      }
    ];

    if (params.query === 'All') {
      return [
        ...baseArticles,
        {
          source: { id: 'marketwatch', name: 'MarketWatch' },
          author: 'Employment Specialist',
          title: 'Unemployment Rate Drops to Multi-Year Low Despite Economic Headwinds',
          description: 'Labor market shows resilience with declining unemployment figures.',
          url: 'https://example.com/unemployment-low',
          urlToImage: null,
          publishedAt: new Date(Date.now() - ********).toISOString(),
          content: 'The unemployment rate has fallen to its lowest level in several years, indicating labor market strength despite broader economic uncertainties. This development could influence central bank policy decisions.'
        },
        {
          source: { id: 'reuters', name: 'Reuters' },
          author: 'Trade Reporter',
          title: 'Trade Balance Shows Improvement as Export Demand Strengthens',
          description: 'Monthly trade figures reveal positive trends in export performance.',
          url: 'https://example.com/trade-balance',
          urlToImage: null,
          publishedAt: new Date(Date.now() - ********).toISOString(),
          content: 'The latest trade balance data shows significant improvement, with exports rising faster than imports. This trend could provide support for the domestic currency and influence monetary policy considerations.'
        }
      ];
    }

    return baseArticles;
  }

  async analyzeSentiment(articles: NewsApiResponse['articles']): Promise<Array<{
    title: string;
    content: string;
    source: string;
    publishedAt: Date;
    url: string;
    sentiment: 'bullish' | 'bearish' | 'neutral';
    impact: 'high' | 'medium' | 'low';
  }>> {
    // Simple keyword-based sentiment analysis
    // In production, you'd want to use a more sophisticated NLP service
    return articles.map(article => {
      const text = `${article.title} ${article.description || ''} ${article.content || ''}`.toLowerCase();
      
      const bullishKeywords = [
        'rise', 'gain', 'increase', 'surge', 'rally', 'strengthen', 'boost',
        'positive', 'optimistic', 'bullish', 'growth', 'recovery', 'upward'
      ];
      
      const bearishKeywords = [
        'fall', 'drop', 'decline', 'plunge', 'weaken', 'crash', 'collapse',
        'negative', 'pessimistic', 'bearish', 'recession', 'crisis', 'downward'
      ];

      const bullishCount = bullishKeywords.filter(keyword => text.includes(keyword)).length;
      const bearishCount = bearishKeywords.filter(keyword => text.includes(keyword)).length;

      let sentiment: 'bullish' | 'bearish' | 'neutral' = 'neutral';
      if (bullishCount > bearishCount) {
        sentiment = 'bullish';
      } else if (bearishCount > bullishCount) {
        sentiment = 'bearish';
      }

      // Simple impact assessment based on source and keywords
      const highImpactKeywords = ['central bank', 'interest rate', 'gdp', 'inflation', 'employment'];
      const hasHighImpact = highImpactKeywords.some(keyword => text.includes(keyword));
      const isReliableSource = ['reuters', 'bloomberg', 'financial times'].includes(article.source.name.toLowerCase());
      
      let impact: 'high' | 'medium' | 'low' = 'low';
      if (hasHighImpact && isReliableSource) {
        impact = 'high';
      } else if (hasHighImpact || isReliableSource) {
        impact = 'medium';
      }

      return {
        title: article.title,
        content: article.content || article.description || '',
        source: article.source.name,
        publishedAt: new Date(article.publishedAt),
        url: article.url,
        sentiment,
        impact,
      };
    });
  }
}

export const newsService = new NewsService();
