import type { 
  User, 
  InsertUser, 
  Analysis, 
  InsertAnalysis, 
  Report, 
  InsertReport, 
  NewsArticle, 
  InsertNewsArticle, 
  AgentStatus, 
  InsertAgentStatus 
} from '@shared/schema';
import { users, analyses, reports, newsArticles, agentStatuses } from '@shared/schema';
import { db } from './db';
import { eq, desc, and } from 'drizzle-orm';

export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Analysis methods
  createAnalysis(analysis: InsertAnalysis): Promise<Analysis>;
  getAnalysis(id: number): Promise<Analysis | undefined>;
  getAllAnalyses(): Promise<Analysis[]>;
  updateAnalysisStatus(id: number, status: string, completedAt?: Date): Promise<void>;
  
  // Report methods
  createReport(report: InsertReport): Promise<Report>;
  getReportsByAnalysisId(analysisId: number): Promise<Report[]>;
  getReportByAnalysisAndAgent(analysisId: number, agentType: string): Promise<Report | undefined>;
  
  // News article methods
  createNewsArticle(article: InsertNewsArticle): Promise<NewsArticle>;
  getNewsArticlesByAnalysisId(analysisId: number): Promise<NewsArticle[]>;
  
  // Agent status methods
  createAgentStatus(status: InsertAgentStatus): Promise<AgentStatus>;
  updateAgentStatus(analysisId: number, agentType: string, status: string, progress?: number, message?: string): Promise<void>;
  getAgentStatusesByAnalysisId(analysisId: number): Promise<AgentStatus[]>;
}

export class DatabaseStorage implements IStorage {
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(insertUser)
      .returning();
    return user;
  }

  async createAnalysis(insertAnalysis: InsertAnalysis): Promise<Analysis> {
    const [analysis] = await db
      .insert(analyses)
      .values({
        ...insertAnalysis,
        status: 'pending',
        createdAt: new Date(),
        completedAt: null,
      })
      .returning();
    return analysis;
  }

  async getAnalysis(id: number): Promise<Analysis | undefined> {
    const [analysis] = await db.select().from(analyses).where(eq(analyses.id, id));
    return analysis || undefined;
  }

  async getAllAnalyses(): Promise<Analysis[]> {
    return await db.select().from(analyses).orderBy(desc(analyses.createdAt));
  }

  async updateAnalysisStatus(id: number, status: string, completedAt?: Date): Promise<void> {
    await db
      .update(analyses)
      .set({ 
        status, 
        completedAt: completedAt || null 
      })
      .where(eq(analyses.id, id));
  }

  async createReport(insertReport: InsertReport): Promise<Report> {
    const [report] = await db
      .insert(reports)
      .values({
        ...insertReport,
        createdAt: new Date(),
      })
      .returning();
    return report;
  }

  async getReportsByAnalysisId(analysisId: number): Promise<Report[]> {
    return await db.select().from(reports).where(eq(reports.analysisId, analysisId));
  }

  async getReportByAnalysisAndAgent(analysisId: number, agentType: string): Promise<Report | undefined> {
    const [report] = await db
      .select()
      .from(reports)
      .where(and(
        eq(reports.analysisId, analysisId),
        eq(reports.agentType, agentType)
      ));
    return report || undefined;
  }

  async createNewsArticle(insertArticle: InsertNewsArticle): Promise<NewsArticle> {
    const [article] = await db
      .insert(newsArticles)
      .values({
        ...insertArticle,
        publishedAt: insertArticle.publishedAt || new Date(),
      })
      .returning();
    return article;
  }

  async getNewsArticlesByAnalysisId(analysisId: number): Promise<NewsArticle[]> {
    return await db.select().from(newsArticles).where(eq(newsArticles.analysisId, analysisId));
  }

  async createAgentStatus(insertStatus: InsertAgentStatus): Promise<AgentStatus> {
    const [status] = await db
      .insert(agentStatuses)
      .values({
        ...insertStatus,
        updatedAt: new Date(),
      })
      .returning();
    return status;
  }

  async updateAgentStatus(analysisId: number, agentType: string, status: string, progress?: number, message?: string): Promise<void> {
    await db
      .update(agentStatuses)
      .set({
        status,
        progress: progress || null,
        message: message || null,
        updatedAt: new Date(),
      })
      .where(and(
        eq(agentStatuses.analysisId, analysisId),
        eq(agentStatuses.agentType, agentType)
      ));
  }

  async getAgentStatusesByAnalysisId(analysisId: number): Promise<AgentStatus[]> {
    return await db.select().from(agentStatuses).where(eq(agentStatuses.analysisId, analysisId));
  }
}

export const storage = new DatabaseStorage();