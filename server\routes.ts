import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { newsService } from "./services/newsService";
import { agentService } from "./services/agentService";
import { reportService } from "./services/reportService";
import { insertAnalysisSchema } from "@shared/schema";
// import { WebSocketServer } from 'ws';

export async function registerRoutes(app: Express): Promise<Server> {
  // Start new analysis
  app.post("/api/analysis", async (req, res) => {
    try {
      const validatedData = insertAnalysisSchema.parse(req.body);
      
      // Create analysis record
      const analysis = await storage.createAnalysis(validatedData);
      
      // Start the analysis workflow asynchronously
      processAnalysisWorkflow(analysis.id).catch(error => {
        console.error('Workflow error:', error);
      });
      
      res.json({ success: true, analysisId: analysis.id });
    } catch (error) {
      console.error('Error creating analysis:', error);
      res.status(400).json({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to create analysis' 
      });
    }
  });

  // Get analysis result with all reports
  app.get("/api/analysis/:id", async (req, res) => {
    try {
      const analysisId = parseInt(req.params.id);
      const result = await reportService.getAnalysisResult(analysisId);
      
      if (!result) {
        return res.status(404).json({ success: false, message: 'Analysis not found' });
      }
      
      res.json({ success: true, data: result });
    } catch (error) {
      console.error('Error fetching analysis:', error);
      res.status(500).json({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to fetch analysis' 
      });
    }
  });

  // Get all analyses
  app.get("/api/analyses", async (req, res) => {
    try {
      const analyses = await reportService.getAllAnalyses();
      res.json({ success: true, data: analyses });
    } catch (error) {
      console.error('Error fetching analyses:', error);
      res.status(500).json({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to fetch analyses' 
      });
    }
  });

  // Get agent statuses for an analysis
  app.get("/api/analysis/:id/status", async (req, res) => {
    try {
      const analysisId = parseInt(req.params.id);
      const statuses = await storage.getAgentStatusesByAnalysisId(analysisId);
      res.json({ success: true, data: statuses });
    } catch (error) {
      console.error('Error fetching agent statuses:', error);
      res.status(500).json({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to fetch agent statuses' 
      });
    }
  });

  // Export analysis as markdown
  app.get("/api/analysis/:id/export", async (req, res) => {
    try {
      const analysisId = parseInt(req.params.id);
      const markdown = await reportService.exportAnalysisToMarkdown(analysisId);
      
      res.setHeader('Content-Type', 'text/markdown');
      res.setHeader('Content-Disposition', `attachment; filename="analysis-${analysisId}.md"`);
      res.send(markdown);
    } catch (error) {
      console.error('Error exporting analysis:', error);
      res.status(500).json({ 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to export analysis' 
      });
    }
  });

  const httpServer = createServer(app);

  // TODO: Re-enable WebSocket for real-time updates after resolving conflicts
  // For now, using polling-based updates

  return httpServer;
}

async function processAnalysisWorkflow(analysisId: number) {
  try {
    const analysis = await storage.getAnalysis(analysisId);
    if (!analysis) throw new Error('Analysis not found');

    // Step 1: Fetch and analyze news
    broadcastStatusUpdate(analysisId, { type: 'workflow', status: 'fetching_news', message: 'Collecting news articles...' });
    
    const articles = await newsService.searchForexNews({
      query: analysis.eventCategory === 'All' ? '' : analysis.searchQuery,
      currencyPair: analysis.currencyPair,
      timeRange: analysis.timeRange,
      sources: analysis.newsSources,
      pageSize: 20,
    });

    const analyzedArticles = await newsService.analyzeSentiment(articles);
    
    // Store news articles
    for (const article of analyzedArticles) {
      await storage.createNewsArticle({
        analysisId,
        title: article.title,
        content: article.content,
        source: article.source,
        publishedAt: article.publishedAt,
        url: article.url,
        sentiment: article.sentiment,
        impact: article.impact,
      });
    }

    broadcastStatusUpdate(analysisId, { 
      type: 'workflow', 
      status: 'news_complete', 
      message: `Collected ${analyzedArticles.length} articles` 
    });

    // Step 2: Run AI agent workflow
    await agentService.runFullWorkflow(analysisId);

    broadcastStatusUpdate(analysisId, { 
      type: 'workflow', 
      status: 'complete', 
      message: 'Analysis workflow completed successfully' 
    });

  } catch (error) {
    console.error('Workflow processing error:', error);
    await storage.updateAnalysisStatus(analysisId, 'failed');
    
    broadcastStatusUpdate(analysisId, { 
      type: 'workflow', 
      status: 'error', 
      message: error instanceof Error ? error.message : 'Workflow failed' 
    });
  }
}

function broadcastStatusUpdate(analysisId: number, update: any) {
  // TODO: Implement WebSocket broadcasting once conflicts are resolved
  // For now, status updates will be available via polling
  console.log(`Status update for analysis ${analysisId}:`, update);
}
