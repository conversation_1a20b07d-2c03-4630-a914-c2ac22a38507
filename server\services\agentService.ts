import { GoogleGenerativeAI } from "@google/generative-ai";
import { storage } from '../storage';
import type { NewsArticle, Analysis } from '@shared/schema';

interface AgentPromptTemplate {
  systemPrompt: string;
  userPromptTemplate: string;
  maxTokens: number;
}

export class AgentService {
  private geminiAI: GoogleGenerativeAI;

  constructor() {
    const apiKey = process.env.GEMINI_API_KEY || '';
    this.geminiAI = new GoogleGenerativeAI(apiKey);
  }

  private templates: Record<string, AgentPromptTemplate> = {
    bullish: {
      systemPrompt: `You are a BullishResearcher specializing in forex market analysis. Your mission is to build the strongest evidence-based BUY thesis for the target currency pair.

Universal Rules:
• Cite every fact (source, date)
• Use the provided Output Template verbatim
• No chit-chat; only the required sections
• When a section is not applicable, write "N/A"
• Stay under 900 words
• All numbers in USD unless stated

Tasks:
• Mine the data for bullish signals
• Augment with fresh analysis: sector tail-winds, tech edge, catalysts, valuation comps
• Pre-empt obvious bearish points (debt, regulation, competition)
• Minimum three primary sources dated within the last 12 months

Output your analysis in the following JSON format:
{
  "executiveSummary": "Brief overview of bullish thesis",
  "keyBullishFactors": ["factor1", "factor2", "factor3"],
  "technicalAnalysis": "Technical indicators supporting bullish view",
  "fundamentalAnalysis": "Economic fundamentals supporting the thesis",
  "catalysts": ["upcoming events that could drive price higher"],
  "riskFactors": ["potential risks to the bullish thesis"],
  "targetPrice": "Price target with timeframe",
  "confidence": "High/Medium/Low",
  "sources": ["source1", "source2", "source3"]
}`,
      userPromptTemplate: `Analyze the following forex market data and news articles to build a bullish case for {currencyPair}.

Currency Pair: {currencyPair}
Search Query: {searchQuery}
Event Category: {eventCategory}

News Articles:
{newsArticles}

Provide a comprehensive bullish analysis following the required JSON format.`,
      maxTokens: 1000,
    },
    bearish: {
      systemPrompt: `You are a BearishResearcher specializing in forex market analysis. Your mission is to build the strongest evidence-based SELL thesis for the target currency pair.

Universal Rules:
• Cite every fact (source, date)
• Use the provided Output Template verbatim
• No chit-chat; only the required sections
• When a section is not applicable, write "N/A"
• Stay under 900 words
• At least one alternative-scenario valuation

Tasks:
• Extract red flags: stretched valuation, margin erosion, governance, litigation, macro headwinds
• Source corroborating evidence: competitor gains, regulatory probes, credit metrics
• Anticipate bullish rebuttals and counter them

Output your analysis in the following JSON format:
{
  "executiveSummary": "Brief overview of bearish thesis",
  "keyBearishFactors": ["factor1", "factor2", "factor3"],
  "technicalAnalysis": "Technical indicators supporting bearish view",
  "fundamentalAnalysis": "Economic fundamentals supporting the thesis",
  "riskEvents": ["potential negative catalysts"],
  "bearishScenarios": ["downside scenarios with probability"],
  "targetPrice": "Price target with timeframe",
  "confidence": "High/Medium/Low",
  "sources": ["source1", "source2", "source3"]
}`,
      userPromptTemplate: `Analyze the following forex market data and news articles to build a bearish case for {currencyPair}.

Currency Pair: {currencyPair}
Search Query: {searchQuery}
Event Category: {eventCategory}

News Articles:
{newsArticles}

Provide a comprehensive bearish analysis following the required JSON format.`,
      maxTokens: 1000,
    },
    risk: {
      systemPrompt: `You are a RiskManager specializing in forex market risk assessment. Your mission is to aggregate and quantify every material risk surfaced in the analysis.

Universal Rules:
• Stick to quantitative language; no opinion on BUY/SELL
• Max 600 words
• List each distinct risk (headline, ≤20 words)
• Assign Impact (Low/Med/High) and Likelihood (Low/Med/High)
• Produce a 3×3 risk matrix
• Summarize critical ("High/High" and "High/Med") risks
• Suggest at least two risk-mitigation tactics

Output your analysis in the following JSON format:
{
  "riskMatrix": [
    {"risk": "Risk description", "impact": "High/Med/Low", "likelihood": "High/Med/Low"},
  ],
  "criticalRisks": ["summary of high impact/high likelihood risks"],
  "riskMitigationTactics": ["tactic1", "tactic2"],
  "overallRiskAssessment": "Low/Medium/High",
  "recommendedPosition": "Conservative/Moderate/Aggressive",
  "maxExposure": "Maximum recommended position size as percentage"
}`,
      userPromptTemplate: `Analyze the following bullish and bearish analyses to provide a comprehensive risk assessment for {currencyPair}.

Currency Pair: {currencyPair}

Bullish Analysis:
{bullishAnalysis}

Bearish Analysis:
{bearishAnalysis}

Provide a comprehensive risk assessment following the required JSON format.`,
      maxTokens: 800,
    },
    trader: {
      systemPrompt: `You are a Trader responsible for issuing the single actionable call (BUY / SELL / HOLD).

Universal Rules:
• Must explicitly reference at least two bullish and two bearish points
• Provide a stop-loss and target if "BUY", or cover price if "SELL"
• Tone: decisive, professional
• Max 500 words
• Assign confidence level

Tasks:
• Weigh credibility and data quality of each side
• Map risk-reward: What's realistic upside vs. plausible drawdown?
• Decide recommendation and assign confidence level

Output your analysis in the following JSON format:
{
  "recommendation": "BUY/SELL/HOLD",
  "confidence": "High/Medium/Low",
  "entryPrice": "Recommended entry price",
  "targetPrice": "Target price",
  "stopLoss": "Stop loss price",
  "timeframe": "Expected holding period",
  "positionSize": "Recommended position size",
  "keyBullishPoints": ["point1", "point2"],
  "keyBearishPoints": ["point1", "point2"],
  "reasoning": "Detailed reasoning for the recommendation",
  "riskReward": "Risk/reward ratio"
}`,
      userPromptTemplate: `Based on the comprehensive analysis below, provide your final trading recommendation for {currencyPair}.

Currency Pair: {currencyPair}

Bullish Analysis:
{bullishAnalysis}

Bearish Analysis:
{bearishAnalysis}

Risk Assessment:
{riskAnalysis}

Provide your final trading recommendation following the required JSON format.`,
      maxTokens: 600,
    },
  };

  async processBullishAnalysis(analysisId: number): Promise<void> {
    await this.updateAgentStatus(analysisId, 'bullish', 'processing', 10);

    try {
      const analysis = await storage.getAnalysis(analysisId);
      if (!analysis) throw new Error('Analysis not found');

      const newsArticles = await storage.getNewsArticlesByAnalysisId(analysisId);
      const formattedNews = this.formatNewsForPrompt(newsArticles);

      await this.updateAgentStatus(analysisId, 'bullish', 'processing', 50, 'Analyzing bullish factors...');

      const content = await this.callAgent('bullish', {
        currencyPair: analysis.currencyPair,
        searchQuery: analysis.searchQuery,
        eventCategory: analysis.eventCategory,
        newsArticles: formattedNews,
      });

      await storage.createReport({
        analysisId,
        agentType: 'bullish',
        content,
        metadata: { timestamp: new Date().toISOString() },
      });

      await this.updateAgentStatus(analysisId, 'bullish', 'completed', 100, 'Bullish analysis complete');
    } catch (error) {
      await this.updateAgentStatus(analysisId, 'bullish', 'failed', 0, `Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    }
  }

  async processBearishAnalysis(analysisId: number): Promise<void> {
    await this.updateAgentStatus(analysisId, 'bearish', 'processing', 10);

    try {
      const analysis = await storage.getAnalysis(analysisId);
      if (!analysis) throw new Error('Analysis not found');

      const newsArticles = await storage.getNewsArticlesByAnalysisId(analysisId);
      const formattedNews = this.formatNewsForPrompt(newsArticles);

      await this.updateAgentStatus(analysisId, 'bearish', 'processing', 50, 'Analyzing bearish factors...');

      const content = await this.callAgent('bearish', {
        currencyPair: analysis.currencyPair,
        searchQuery: analysis.searchQuery,
        eventCategory: analysis.eventCategory,
        newsArticles: formattedNews,
      });

      await storage.createReport({
        analysisId,
        agentType: 'bearish',
        content,
        metadata: { timestamp: new Date().toISOString() },
      });

      await this.updateAgentStatus(analysisId, 'bearish', 'completed', 100, 'Bearish analysis complete');
    } catch (error) {
      await this.updateAgentStatus(analysisId, 'bearish', 'failed', 0, `Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    }
  }

  async processRiskAnalysis(analysisId: number): Promise<void> {
    await this.updateAgentStatus(analysisId, 'risk', 'processing', 10);

    try {
      const analysis = await storage.getAnalysis(analysisId);
      if (!analysis) throw new Error('Analysis not found');

      const bullishReport = await storage.getReportByAnalysisAndAgent(analysisId, 'bullish');
      const bearishReport = await storage.getReportByAnalysisAndAgent(analysisId, 'bearish');

      if (!bullishReport || !bearishReport) {
        throw new Error('Missing prerequisite reports');
      }

      await this.updateAgentStatus(analysisId, 'risk', 'processing', 50, 'Assessing risk factors...');

      const content = await this.callAgent('risk', {
        currencyPair: analysis.currencyPair,
        bullishAnalysis: bullishReport.content,
        bearishAnalysis: bearishReport.content,
      });

      await storage.createReport({
        analysisId,
        agentType: 'risk',
        content,
        metadata: { timestamp: new Date().toISOString() },
      });

      await this.updateAgentStatus(analysisId, 'risk', 'completed', 100, 'Risk analysis complete');
    } catch (error) {
      await this.updateAgentStatus(analysisId, 'risk', 'failed', 0, `Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    }
  }

  async processTraderRecommendation(analysisId: number): Promise<void> {
    await this.updateAgentStatus(analysisId, 'trader', 'processing', 10);

    try {
      const analysis = await storage.getAnalysis(analysisId);
      if (!analysis) throw new Error('Analysis not found');

      const bullishReport = await storage.getReportByAnalysisAndAgent(analysisId, 'bullish');
      const bearishReport = await storage.getReportByAnalysisAndAgent(analysisId, 'bearish');
      const riskReport = await storage.getReportByAnalysisAndAgent(analysisId, 'risk');

      if (!bullishReport || !bearishReport || !riskReport) {
        throw new Error('Missing prerequisite reports');
      }

      await this.updateAgentStatus(analysisId, 'trader', 'processing', 50, 'Generating final recommendation...');

      const content = await this.callAgent('trader', {
        currencyPair: analysis.currencyPair,
        bullishAnalysis: bullishReport.content,
        bearishAnalysis: bearishReport.content,
        riskAnalysis: riskReport.content,
      });

      await storage.createReport({
        analysisId,
        agentType: 'trader',
        content,
        metadata: { timestamp: new Date().toISOString() },
      });

      await this.updateAgentStatus(analysisId, 'trader', 'completed', 100, 'Final recommendation complete');
      await storage.updateAnalysisStatus(analysisId, 'completed', new Date());
    } catch (error) {
      await this.updateAgentStatus(analysisId, 'trader', 'failed', 0, `Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    }
  }

  private async callAgent(agentType: string, variables: Record<string, any>): Promise<string> {
    const template = this.templates[agentType];
    if (!template) {
      throw new Error(`Unknown agent type: ${agentType}`);
    }

    let userPrompt = template.userPromptTemplate;
    Object.entries(variables).forEach(([key, value]) => {
      userPrompt = userPrompt.replace(new RegExp(`{${key}}`, 'g'), value);
    });

    const model = this.geminiAI.getGenerativeModel({
      model: "gemini-1.5-pro",
      systemInstruction: template.systemPrompt,
      generationConfig: {
        responseMimeType: "application/json",
        maxOutputTokens: template.maxTokens,
        temperature: 0.7,
      },
    });

    const response = await model.generateContent(userPrompt);
    
    const content = response.response.text();
    if (!content) {
      throw new Error(`No response from ${agentType} agent`);
    }

    return content;
  }

  private formatNewsForPrompt(articles: NewsArticle[]): string {
    return articles.map(article => 
      `Title: ${article.title}
Source: ${article.source}
Published: ${article.publishedAt}
Sentiment: ${article.sentiment}
Impact: ${article.impact}
Content: ${article.content.substring(0, 500)}...
URL: ${article.url}
---`
    ).join('\n\n');
  }

  private async updateAgentStatus(analysisId: number, agentType: string, status: string, progress?: number, message?: string): Promise<void> {
    await storage.updateAgentStatus(analysisId, agentType, status, progress, message);
  }

  async runFullWorkflow(analysisId: number): Promise<void> {
    try {
      // Initialize all agent statuses
      const agentTypes = ['bullish', 'bearish', 'risk', 'trader'];
      for (const agentType of agentTypes) {
        await storage.createAgentStatus({
          analysisId,
          agentType,
          status: 'waiting',
          progress: 0,
        });
      }

      await storage.updateAnalysisStatus(analysisId, 'processing');

      // Run agents sequentially
      await this.processBullishAnalysis(analysisId);
      await this.processBearishAnalysis(analysisId);
      await this.processRiskAnalysis(analysisId);
      await this.processTraderRecommendation(analysisId);

    } catch (error) {
      console.error('Workflow failed:', error);
      await storage.updateAnalysisStatus(analysisId, 'failed');
      throw error;
    }
  }
}

export const agentService = new AgentService();
