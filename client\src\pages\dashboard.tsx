import { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { useQuery } from '@tanstack/react-query';
import SearchForm from '@/components/SearchForm';
import WorkflowProgress from '@/components/WorkflowProgress';
import ResultsTabs from '@/components/ResultsTabs';
import AgentStatus from '@/components/AgentStatus';
import { Button } from '@/components/ui/button';
import { ChartLine, Plus, User, Search, Bot, FileText, History } from 'lucide-react';

export default function Dashboard() {
  const [location, navigate] = useLocation();
  const [currentAnalysisId, setCurrentAnalysisId] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState('news');
  const [showNewAnalysis, setShowNewAnalysis] = useState(true);

  // Extract analysis ID from URL
  useEffect(() => {
    const match = location.match(/\/analysis\/(\d+)/);
    if (match) {
      const analysisId = parseInt(match[1]);
      setCurrentAnalysisId(analysisId);
      setShowNewAnalysis(false);
      // console.log('URL changed, setting analysis ID:', analysisId);
    } else {
      setCurrentAnalysisId(null);
      setShowNewAnalysis(true);
      // console.log('URL changed, showing new analysis form');
    }
  }, [location]);

  const { data: analyses } = useQuery({
    queryKey: ['/api/analyses'],
    enabled: true,
  });

  const { data: currentAnalysis, isLoading, error } = useQuery({
    queryKey: [`/api/analysis/${currentAnalysisId}`],
    enabled: !!currentAnalysisId,
    refetchInterval: currentAnalysisId ? 5000 : false, // Poll every 5 seconds for active analysis
    retry: 3,
    retryDelay: 1000,
  });

  // Remove debug logging
  // console.log('Dashboard state:', { currentAnalysisId, showNewAnalysis, isLoading, hasCurrentAnalysis: !!currentAnalysis?.data });

  const handleNewAnalysis = () => {
    setShowNewAnalysis(true);
    setCurrentAnalysisId(null);
    navigate('/');
  };

  const handleAnalysisCreated = (analysisId: number) => {
    setCurrentAnalysisId(analysisId);
    setShowNewAnalysis(false);
    setActiveTab('news');
    navigate(`/analysis/${analysisId}`);
  };

  const handleSelectAnalysis = (analysisId: number) => {
    setCurrentAnalysisId(analysisId);
    setShowNewAnalysis(false);
    setActiveTab('news');
    navigate(`/analysis/${analysisId}`);
  };

  return (
    <div className="bg-background min-h-screen font-sans">
      {/* Header */}
      <header className="bg-surface shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                <ChartLine className="text-white h-5 w-5" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">Forex MacroEvent Trader</h1>
                <p className="text-sm text-gray-500">AI-Powered Market Analysis</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button 
                onClick={handleNewAnalysis}
                className="bg-primary text-white hover:bg-blue-700"
              >
                <Plus className="mr-2 h-4 w-4" />
                New Analysis
              </Button>
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                <User className="h-4 w-4 text-gray-600" />
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="flex h-screen pt-16">
        {/* Sidebar */}
        <aside className="w-64 bg-surface shadow-sm border-r border-gray-200 flex flex-col">
          <div className="p-6">
            <nav className="space-y-2">
              <button
                onClick={handleNewAnalysis}
                className={`flex items-center w-full px-3 py-2 rounded-lg ${
                  showNewAnalysis ? 'text-primary bg-blue-50' : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <Search className="mr-3 h-4 w-4" />
                News Search
              </button>
              <button 
                onClick={() => {
                  if (currentAnalysisId) {
                    setActiveTab('bullish');
                    setShowNewAnalysis(false);
                  }
                }}
                className="flex items-center w-full px-3 py-2 text-gray-600 hover:bg-gray-50 rounded-lg"
              >
                <Bot className="mr-3 h-4 w-4" />
                Agent Workflow
              </button>
              <button 
                onClick={() => {
                  if (currentAnalysisId) {
                    setActiveTab('trader');
                    setShowNewAnalysis(false);
                  }
                }}
                className="flex items-center w-full px-3 py-2 text-gray-600 hover:bg-gray-50 rounded-lg"
              >
                <FileText className="mr-3 h-4 w-4" />
                Reports
              </button>
              <button 
                onClick={() => setShowNewAnalysis(false)}
                className="flex items-center w-full px-3 py-2 text-gray-600 hover:bg-gray-50 rounded-lg"
              >
                <History className="mr-3 h-4 w-4" />
                History
              </button>
            </nav>

            {/* Recent Analyses */}
            {analyses?.data && analyses.data.length > 0 && (
              <div className="mt-6 pt-6 border-t border-gray-200">
                <h3 className="text-sm font-medium text-gray-900 mb-3">Recent Analyses</h3>
                <div className="space-y-2">
                  {analyses.data.slice(0, 5).map((analysis: any) => (
                    <button
                      key={analysis.id}
                      onClick={() => handleSelectAnalysis(analysis.id)}
                      className={`w-full text-left px-2 py-1 text-xs rounded hover:bg-gray-100 ${
                        currentAnalysisId === analysis.id ? 'bg-blue-50 text-primary' : 'text-gray-600'
                      }`}
                    >
                      <div className="font-medium">{analysis.currencyPair}</div>
                      <div className="text-gray-500 truncate">
                        {analysis.searchQuery || analysis.eventCategory}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Agent Status Panel */}
          {currentAnalysisId && (
            <AgentStatus analysisId={currentAnalysisId} />
          )}
        </aside>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto">
          <div className="max-w-6xl mx-auto p-6">
            {showNewAnalysis ? (
              <SearchForm onAnalysisCreated={handleAnalysisCreated} />
            ) : currentAnalysisId && currentAnalysis?.data ? (
              <div className="space-y-8">
                <WorkflowProgress 
                  analysis={currentAnalysis.data.analysis}
                  agentStatuses={currentAnalysis.data.agentStatuses || []}
                />
                <ResultsTabs 
                  analysisResult={currentAnalysis.data}
                  isLoading={isLoading}
                  activeTab={activeTab}
                  onTabChange={setActiveTab}
                />
              </div>
            ) : currentAnalysisId ? (
              <div className="text-center py-12">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
                </div>
                <p className="mt-4 text-sm text-gray-500">Loading analysis results...</p>
              </div>
            ) : (
              <div className="text-center py-12">
                <ChartLine className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No analysis selected</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Start a new analysis or select an existing one from the sidebar.
                </p>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
}
