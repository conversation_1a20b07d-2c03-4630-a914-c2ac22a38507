import { useQuery } from '@tanstack/react-query';

interface AgentStatusProps {
  analysisId: number;
}

export default function AgentStatus({ analysisId }: AgentStatusProps) {
  const { data: statuses } = useQuery({
    queryKey: ['/api/analysis', analysisId, 'status'],
    enabled: !!analysisId,
    refetchInterval: 3000, // Poll every 3 seconds
  });

  const agentOrder = ['bullish', 'bearish', 'risk', 'trader'];
  const agentLabels = {
    bullish: 'Bullish Agent',
    bearish: 'Bearish Agent',
    risk: 'Risk Manager',
    trader: 'Trader'
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "px-2 py-1 text-xs rounded-full";
    
    switch (status) {
      case 'completed':
        return `${baseClasses} status-completed`;
      case 'processing':
        return `${baseClasses} status-processing`;
      case 'failed':
        return `${baseClasses} status-failed`;
      default:
        return `${baseClasses} status-waiting`;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Complete';
      case 'processing':
        return 'Processing';
      case 'failed':
        return 'Failed';
      default:
        return 'Waiting';
    }
  };

  return (
    <div className="p-6 border-t border-gray-200 mt-auto">
      <h3 className="text-sm font-medium text-gray-900 mb-3">Current Analysis</h3>
      <div className="space-y-3">
        {agentOrder.map(agentType => {
          const status = statuses?.data?.find((s: any) => s.agentType === agentType);
          const statusValue = status?.status || 'waiting';
          
          return (
            <div key={agentType} className="flex items-center justify-between">
              <span className="text-sm text-gray-600">
                {agentLabels[agentType as keyof typeof agentLabels]}
              </span>
              <span className={getStatusBadge(statusValue)}>
                {getStatusText(statusValue)}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
}
