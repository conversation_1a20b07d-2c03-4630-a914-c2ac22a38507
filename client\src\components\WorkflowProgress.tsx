import { Card, CardContent } from '@/components/ui/card';
import { Check, Loader2, AlertCircle, Info } from 'lucide-react';

interface WorkflowProgressProps {
  analysis: any;
  agentStatuses: any[];
}

export default function WorkflowProgress({ analysis, agentStatuses }: WorkflowProgressProps) {
  if (!analysis) return null;

  const getStepStatus = (agentType: string) => {
    const status = agentStatuses.find(s => s.agentType === agentType);
    return status?.status || 'waiting';
  };

  const getStepMessage = (agentType: string) => {
    const status = agentStatuses.find(s => s.agentType === agentType);
    return status?.message || '';
  };

  const getStepProgress = (agentType: string) => {
    const status = agentStatuses.find(s => s.agentType === agentType);
    return status?.progress || 0;
  };

  const steps = [
    { 
      key: 'news', 
      label: 'News Collection', 
      agentType: null,
      status: analysis.status === 'pending' ? 'waiting' : 'completed'
    },
    { key: 'bullish', label: 'Bullish Analysis', agentType: 'bullish' },
    { key: 'bearish', label: 'Bearish Analysis', agentType: 'bearish' },
    { key: 'risk', label: 'Risk Assessment', agentType: 'risk' },
    { key: 'trader', label: 'Final Recommendation', agentType: 'trader' },
  ];

  const getStepIcon = (step: any) => {
    const status = step.agentType ? getStepStatus(step.agentType) : step.status;
    
    switch (status) {
      case 'completed':
        return <Check className="text-white h-3 w-3" />;
      case 'processing':
        return <Loader2 className="text-white h-3 w-3 animate-spin" />;
      case 'failed':
        return <AlertCircle className="text-white h-3 w-3" />;
      default:
        return <span className="text-white text-xs font-medium">{steps.findIndex(s => s.key === step.key) + 1}</span>;
    }
  };

  const getStepColor = (step: any) => {
    const status = step.agentType ? getStepStatus(step.agentType) : step.status;
    
    switch (status) {
      case 'completed':
        return 'bg-success';
      case 'processing':
        return 'bg-warning';
      case 'failed':
        return 'bg-error';
      default:
        return 'bg-gray-300';
    }
  };

  const getConnectorColor = (index: number) => {
    if (index >= steps.length - 1) return '';
    
    const currentStep = steps[index];
    const currentStatus = currentStep.agentType ? getStepStatus(currentStep.agentType) : currentStep.status;
    
    return currentStatus === 'completed' ? 'bg-success' : 'bg-gray-300';
  };

  const getCurrentMessage = () => {
    // Find the currently processing step
    const processingStep = steps.find(step => {
      const status = step.agentType ? getStepStatus(step.agentType) : step.status;
      return status === 'processing';
    });

    if (processingStep) {
      const message = processingStep.agentType ? getStepMessage(processingStep.agentType) : 'Processing...';
      return {
        title: `${processingStep.label} Processing`,
        message: message || 'Analyzing data and generating insights...',
      };
    }

    if (analysis.status === 'completed') {
      return {
        title: 'Analysis Complete',
        message: 'All agents have completed their analysis. Review the results below.',
      };
    }

    if (analysis.status === 'failed') {
      return {
        title: 'Analysis Failed',
        message: 'An error occurred during the analysis workflow.',
      };
    }

    return {
      title: 'Analysis Starting',
      message: 'Initializing the multi-agent analysis workflow...',
    };
  };

  const currentMessage = getCurrentMessage();

  return (
    <section className="mb-8">
      <Card className="bg-surface shadow-sm border border-gray-200">
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Analysis Workflow</h3>
          
          <div className="flex items-center justify-between mb-6">
            {steps.map((step, index) => (
              <div key={step.key} className="flex items-center">
                <div className="flex items-center space-x-2">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getStepColor(step)}`}>
                    {getStepIcon(step)}
                  </div>
                  <span className="text-sm font-medium text-gray-900 hidden sm:block">{step.label}</span>
                  <span className="text-sm font-medium text-gray-900 sm:hidden">{index + 1}</span>
                </div>
                {index < steps.length - 1 && (
                  <div className={`flex-1 h-0.5 mx-4 ${getConnectorColor(index)}`}></div>
                )}
              </div>
            ))}
          </div>

          <div className={`rounded-lg p-4 ${
            analysis.status === 'failed' ? 'bg-red-50 border border-red-200' : 'bg-blue-50 border border-blue-200'
          }`}>
            <div className="flex items-center">
              {analysis.status === 'failed' ? (
                <AlertCircle className="text-error mr-3 h-5 w-5" />
              ) : (
                <Info className="text-primary mr-3 h-5 w-5" />
              )}
              <div>
                <p className={`text-sm font-medium ${
                  analysis.status === 'failed' ? 'text-error' : 'text-primary'
                }`}>
                  {currentMessage.title}
                </p>
                <p className={`text-sm ${
                  analysis.status === 'failed' ? 'text-red-600' : 'text-blue-600'
                }`}>
                  {currentMessage.message}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </section>
  );
}
