import { storage } from '../storage';
import type { Analysis, Report, NewsArticle, AgentStatus } from '@shared/schema';

export interface AnalysisResult {
  analysis: Analysis;
  reports: Record<string, Report>;
  newsArticles: NewsArticle[];
  agentStatuses: AgentStatus[];
  summary: {
    bullishSentiment: number;
    bearishSentiment: number;
    neutralSentiment: number;
    riskLevel: string;
    confidenceScore: number;
    recommendation?: string;
  };
}

export class ReportService {
  async getAnalysisResult(analysisId: number): Promise<AnalysisResult | null> {
    const analysis = await storage.getAnalysis(analysisId);
    if (!analysis) return null;

    const reports = await storage.getReportsByAnalysisId(analysisId);
    const newsArticles = await storage.getNewsArticlesByAnalysisId(analysisId);
    const agentStatuses = await storage.getAgentStatusesByAnalysisId(analysisId);

    const reportsMap: Record<string, Report> = {};
    reports.forEach(report => {
      reportsMap[report.agentType] = report;
    });

    const summary = this.generateAnalysisSummary(newsArticles, reportsMap);

    return {
      analysis,
      reports: reportsMap,
      newsArticles,
      agentStatuses,
      summary,
    };
  }

  private generateAnalysisSummary(newsArticles: NewsArticle[], reports: Record<string, Report>) {
    // Calculate sentiment distribution
    const sentimentCounts = newsArticles.reduce(
      (acc, article) => {
        if (article.sentiment === 'bullish') acc.bullish++;
        else if (article.sentiment === 'bearish') acc.bearish++;
        else acc.neutral++;
        return acc;
      },
      { bullish: 0, bearish: 0, neutral: 0 }
    );

    const total = newsArticles.length || 1;
    const bullishSentiment = Math.round((sentimentCounts.bullish / total) * 100);
    const bearishSentiment = Math.round((sentimentCounts.bearish / total) * 100);
    const neutralSentiment = Math.round((sentimentCounts.neutral / total) * 100);

    // Extract risk level and confidence from reports
    let riskLevel = 'Medium';
    let confidenceScore = 75;
    let recommendation = undefined;

    try {
      if (reports.risk?.content) {
        const riskData = JSON.parse(reports.risk.content);
        riskLevel = riskData.overallRiskAssessment || 'Medium';
      }

      if (reports.trader?.content) {
        const traderData = JSON.parse(reports.trader.content);
        recommendation = traderData.recommendation;
        
        // Convert confidence to percentage
        const confidenceMap: Record<string, number> = {
          'High': 85,
          'Medium': 65,
          'Low': 45,
        };
        confidenceScore = confidenceMap[traderData.confidence] || 65;
      }
    } catch (error) {
      console.warn('Error parsing report content for summary:', error);
    }

    return {
      bullishSentiment,
      bearishSentiment,
      neutralSentiment,
      riskLevel,
      confidenceScore,
      recommendation,
    };
  }

  async getAllAnalyses(): Promise<Analysis[]> {
    return await storage.getAllAnalyses();
  }

  async exportAnalysisToMarkdown(analysisId: number): Promise<string> {
    const result = await this.getAnalysisResult(analysisId);
    if (!result) throw new Error('Analysis not found');

    const { analysis, reports, newsArticles, summary } = result;

    let markdown = `# Forex Analysis Report: ${analysis.currencyPair}\n\n`;
    markdown += `**Generated:** ${analysis.createdAt.toLocaleString()}\n`;
    markdown += `**Query:** ${analysis.searchQuery}\n`;
    markdown += `**Category:** ${analysis.eventCategory}\n`;
    markdown += `**Status:** ${analysis.status}\n\n`;

    // Summary section
    markdown += `## Executive Summary\n\n`;
    markdown += `- **Bullish Sentiment:** ${summary.bullishSentiment}%\n`;
    markdown += `- **Bearish Sentiment:** ${summary.bearishSentiment}%\n`;
    markdown += `- **Risk Level:** ${summary.riskLevel}\n`;
    markdown += `- **Confidence Score:** ${summary.confidenceScore}%\n`;
    if (summary.recommendation) {
      markdown += `- **Recommendation:** ${summary.recommendation}\n`;
    }
    markdown += `\n`;

    // News articles section
    markdown += `## News Analysis (${newsArticles.length} articles)\n\n`;
    newsArticles.forEach(article => {
      markdown += `### ${article.title}\n`;
      markdown += `**Source:** ${article.source} | **Published:** ${article.publishedAt.toLocaleString()}\n`;
      markdown += `**Sentiment:** ${article.sentiment} | **Impact:** ${article.impact}\n`;
      markdown += `**URL:** ${article.url}\n\n`;
      markdown += `${article.content.substring(0, 300)}...\n\n`;
    });

    // Agent reports
    const agentOrder = ['bullish', 'bearish', 'risk', 'trader'];
    const agentTitles = {
      bullish: 'Bullish Analysis',
      bearish: 'Bearish Analysis', 
      risk: 'Risk Assessment',
      trader: 'Trading Recommendation'
    };

    agentOrder.forEach(agentType => {
      if (reports[agentType]) {
        markdown += `## ${agentTitles[agentType as keyof typeof agentTitles]}\n\n`;
        try {
          const reportData = JSON.parse(reports[agentType].content);
          markdown += this.formatReportData(agentType, reportData);
        } catch (error) {
          markdown += `${reports[agentType].content}\n\n`;
        }
      }
    });

    return markdown;
  }

  private formatReportData(agentType: string, data: any): string {
    let formatted = '';

    switch (agentType) {
      case 'bullish':
        formatted += `**Executive Summary:** ${data.executiveSummary}\n\n`;
        formatted += `**Key Bullish Factors:**\n${data.keyBullishFactors?.map((f: string) => `- ${f}`).join('\n') || 'N/A'}\n\n`;
        formatted += `**Technical Analysis:** ${data.technicalAnalysis}\n\n`;
        formatted += `**Target Price:** ${data.targetPrice}\n`;
        formatted += `**Confidence:** ${data.confidence}\n\n`;
        break;

      case 'bearish':
        formatted += `**Executive Summary:** ${data.executiveSummary}\n\n`;
        formatted += `**Key Bearish Factors:**\n${data.keyBearishFactors?.map((f: string) => `- ${f}`).join('\n') || 'N/A'}\n\n`;
        formatted += `**Technical Analysis:** ${data.technicalAnalysis}\n\n`;
        formatted += `**Target Price:** ${data.targetPrice}\n`;
        formatted += `**Confidence:** ${data.confidence}\n\n`;
        break;

      case 'risk':
        formatted += `**Overall Risk Assessment:** ${data.overallRiskAssessment}\n`;
        formatted += `**Recommended Position:** ${data.recommendedPosition}\n`;
        formatted += `**Max Exposure:** ${data.maxExposure}\n\n`;
        formatted += `**Critical Risks:**\n${data.criticalRisks?.map((r: string) => `- ${r}`).join('\n') || 'N/A'}\n\n`;
        break;

      case 'trader':
        formatted += `**Recommendation:** ${data.recommendation}\n`;
        formatted += `**Confidence:** ${data.confidence}\n`;
        formatted += `**Entry Price:** ${data.entryPrice}\n`;
        formatted += `**Target Price:** ${data.targetPrice}\n`;
        formatted += `**Stop Loss:** ${data.stopLoss}\n`;
        formatted += `**Risk/Reward:** ${data.riskReward}\n\n`;
        formatted += `**Reasoning:** ${data.reasoning}\n\n`;
        break;
    }

    return formatted;
  }
}

export const reportService = new ReportService();
