import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Download, Share2, ChevronDown, ExternalLink } from 'lucide-react';

interface ResultsTabsProps {
  analysisResult: any;
  isLoading: boolean;
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export default function ResultsTabs({ analysisResult, isLoading, activeTab, onTabChange }: ResultsTabsProps) {
  const [showAllArticles, setShowAllArticles] = useState(false);

  if (isLoading || !analysisResult) {
    return (
      <section>
        <Card className="bg-surface shadow-sm border border-gray-200">
          <CardContent className="p-6">
            <Skeleton className="h-8 w-64 mb-4" />
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </CardContent>
        </Card>
      </section>
    );
  }

  const { analysis, reports, newsArticles, summary } = analysisResult;

  const tabs = [
    { id: 'news', label: 'News Summary' },
    { id: 'bullish', label: 'Bull Case Report' },
    { id: 'bearish', label: 'Bear Case Report' },
    { id: 'risk', label: 'Risk Assessment' },
    { id: 'trader', label: 'Final Recommendation' },
  ];

  const handleExport = async () => {
    try {
      const response = await fetch(`/api/analysis/${analysis.id}/export`);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analysis-${analysis.id}.md`;
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const renderNewsContent = () => {
    if (!newsArticles || newsArticles.length === 0) {
      return (
        <div className="text-center py-8">
          <p className="text-gray-500">No news articles found</p>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-700">{summary?.bullishSentiment || 0}%</div>
            <div className="text-sm text-green-600">Bullish Sentiment</div>
          </div>
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-red-700">{summary?.bearishSentiment || 0}%</div>
            <div className="text-sm text-red-600">Bearish Sentiment</div>
          </div>
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-gray-700">{summary?.neutralSentiment || 0}%</div>
            <div className="text-sm text-gray-600">Neutral Sentiment</div>
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            News Articles ({newsArticles?.length || 0})
          </h3>
          <div className="space-y-4">
            {(showAllArticles ? newsArticles : newsArticles?.slice(0, 3))?.map((article: any, index: number) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-gray-900 flex-1">{article.title}</h4>
                  <div className="flex items-center space-x-2 ml-4">
                    <Badge 
                      variant={article.sentiment === 'bullish' ? 'default' : article.sentiment === 'bearish' ? 'destructive' : 'secondary'}
                      className="text-xs"
                    >
                      {article.sentiment}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {article.impact}
                    </Badge>
                  </div>
                </div>
                <p className="text-gray-600 text-sm mb-2">{article.content}</p>
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>{article.source}</span>
                  <span>{new Date(article.publishedAt).toLocaleDateString()}</span>
                </div>
              </div>
            ))}
          </div>
          
          {newsArticles && newsArticles.length > 3 && (
            <Button 
              variant="outline" 
              onClick={() => setShowAllArticles(!showAllArticles)}
              className="w-full mt-4"
            >
              <ChevronDown className="mr-2 h-4 w-4" />
              {showAllArticles ? 'Show Less' : `Show All ${newsArticles.length} Articles`}
            </Button>
          )}
        </div>
      </div>
    );
  };

  const renderAgentReport = (agentType: string) => {
    if (!reports || !reports[agentType]) {
      return <div className="text-center py-8 text-gray-500">Report not available yet</div>;
    }
    
    const report = reports[agentType];

    let data;
    try {
      data = typeof report.content === 'string' ? JSON.parse(report.content) : report.content;
    } catch (error) {
      return (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <h4 className="font-semibold text-gray-900 mb-2">{agentType.charAt(0).toUpperCase() + agentType.slice(1)} Analysis</h4>
          <div className="text-gray-700 whitespace-pre-wrap">{report.content}</div>
        </div>
      );
    }

    switch (agentType) {
      case 'bullish':
        return (
          <div className="space-y-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="font-semibold text-green-800 mb-2">Executive Summary</h4>
              <p className="text-green-700">{data.executiveSummary}</p>
            </div>
            
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Key Bullish Factors</h4>
              <ul className="space-y-2">
                {data.keyBullishFactors?.map((factor: string, index: number) => (
                  <li key={index} className="flex items-start">
                    <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    <span className="text-gray-700">{factor}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Technical Analysis</h4>
                <p className="text-gray-700">{data.technicalAnalysis}</p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Target & Confidence</h4>
                <p className="text-gray-700">Target: {data.targetPrice}</p>
                <p className="text-gray-700">Confidence: {data.confidence}</p>
              </div>
            </div>
          </div>
        );

      case 'bearish':
        return (
          <div className="space-y-6">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h4 className="font-semibold text-red-800 mb-2">Executive Summary</h4>
              <p className="text-red-700">{data.executiveSummary}</p>
            </div>
            
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Key Bearish Factors</h4>
              <ul className="space-y-2">
                {data.keyBearishFactors?.map((factor: string, index: number) => (
                  <li key={index} className="flex items-start">
                    <span className="w-2 h-2 bg-red-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    <span className="text-gray-700">{factor}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Technical Analysis</h4>
                <p className="text-gray-700">{data.technicalAnalysis}</p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Target & Confidence</h4>
                <p className="text-gray-700">Target: {data.targetPrice}</p>
                <p className="text-gray-700">Confidence: {data.confidence}</p>
              </div>
            </div>
          </div>
        );

      case 'risk':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                <div className="text-lg font-semibold text-yellow-700">{data.overallRiskAssessment}</div>
                <div className="text-sm text-yellow-600">Overall Risk</div>
              </div>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                <div className="text-lg font-semibold text-blue-700">{data.recommendedPosition}</div>
                <div className="text-sm text-blue-600">Position Type</div>
              </div>
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
                <div className="text-lg font-semibold text-gray-700">{data.timeframe}</div>
                <div className="text-sm text-gray-600">Time Horizon</div>
              </div>
            </div>

            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Critical Risks</h4>
              <ul className="space-y-2">
                {data.criticalRisks?.map((risk: string, index: number) => (
                  <li key={index} className="flex items-start">
                    <span className="w-2 h-2 bg-yellow-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    <span className="text-gray-700">{risk}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Risk Mitigation</h4>
              <ul className="space-y-2">
                {data.riskMitigationTactics?.map((tactic: string, index: number) => (
                  <li key={index} className="flex items-start">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    <span className="text-gray-700">{tactic}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        );

      case 'trader':
        return (
          <div className="space-y-6">
            <div className={`border rounded-lg p-6 ${
              data.recommendation === 'BUY' ? 'bg-green-50 border-green-200' :
              data.recommendation === 'SELL' ? 'bg-red-50 border-red-200' :
              'bg-yellow-50 border-yellow-200'
            }`}>
              <div className="text-center">
                <div className={`text-3xl font-bold mb-2 ${
                  data.recommendation === 'BUY' ? 'text-green-700' :
                  data.recommendation === 'SELL' ? 'text-red-700' :
                  'text-yellow-700'
                }`}>
                  {data.recommendation}
                </div>
                <div className="text-lg font-medium text-gray-700">
                  Confidence: {data.confidence}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">Trade Setup</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Entry Price:</span>
                    <span className="font-medium">{data.entryPrice}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Target Price:</span>
                    <span className="font-medium">{data.targetPrice}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Stop Loss:</span>
                    <span className="font-medium">{data.stopLoss}</span>
                  </div>
                </div>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">Position Details</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Risk/Reward:</span>
                    <span className="font-medium">{data.riskRewardRatio}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Timeframe:</span>
                    <span className="font-medium">{data.timeframe}</span>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Trading Rationale</h4>
              <p className="text-gray-700">{data.tradingRationale}</p>
            </div>
          </div>
        );

      default:
        return <div className="text-center py-8 text-gray-500">Report format not recognized</div>;
    }
  };

  return (
    <section>
      <Card className="bg-surface shadow-sm border border-gray-200">
        <CardContent className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-semibold text-gray-900">Analysis Results</h2>
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleExport}
              >
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
              <Button variant="outline" size="sm">
                <Share2 className="mr-2 h-4 w-4" />
                Share
              </Button>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="border-b border-gray-200 mb-6">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => onTabChange(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-600 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          {activeTab === 'news' ? renderNewsContent() : renderAgentReport(activeTab)}
        </CardContent>
      </Card>
    </section>
  );
}