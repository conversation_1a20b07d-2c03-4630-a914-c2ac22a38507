# Replit Project Documentation

## Project Overview
**Macroeconomic Event Trader** - A sophisticated multi-agent AI analysis system for financial markets that automatically analyzes news and generates trading insights for any asset including forex, commodities, cryptocurrencies, and stocks.

**Current Status**: Production-ready with full PostgreSQL database integration, RSS news feeds, and multi-asset support.

## Architecture Summary
- **Frontend**: React + TypeScript + Tailwind CSS + Shadcn/ui
- **Backend**: Node.js + Express + TypeScript  
- **Database**: PostgreSQL with Drizzle ORM
- **AI Integration**: Google Gemini-2.5-pro (4 specialized agents)
- **News Sources**: 8+ professional RSS feeds + News API fallback

## Recent Changes (Last Updated: June 23, 2025)

### ✅ RSS Feed Integration (June 23, 2025)
- Successfully integrated 8 professional financial RSS feeds
- ForexLive, ActionForex, Currency Thoughts, MarketWatch, Investing.com
- Increased news coverage from 3 mock articles to 9+ real articles per analysis
- Built robust XML parsing system with error handling
- RSS feeds now provide authentic, real-time market news

### ✅ Multi-Asset Support (June 23, 2025)  
- Replaced currency pair dropdown with free-text input
- Added support for any financial asset: forex, commodities, crypto, stocks
- Enhanced news search algorithms for different asset classes
- Added intelligent asset term mapping (Gold, Oil, BTC, AAPL, etc.)
- Expanded database schema to handle longer asset names

### ✅ Database Migration (Previously Completed)
- Migrated from in-memory storage to PostgreSQL 
- Implemented full persistence for analyses, reports, news articles
- Added real-time agent status tracking
- All CRUD operations working correctly with proper relationships

### ✅ Multi-Agent AI System (Core Feature)
- Four specialized Gemini-2.5-pro agents: Bullish, Bearish, Risk, Trader
- Real-time workflow processing with status updates
- Comprehensive analysis reports with confidence scores
- WebSocket integration for live progress tracking

## User Preferences
- Prefers comprehensive, production-ready solutions
- Values real-time data over mock/placeholder content
- Wants multi-asset support beyond just forex pairs
- Appreciates detailed technical explanations
- Focuses on authentic news sources and RSS integration

## Technical Decisions Made
1. **AI Model Choice**: Google Gemini-2.5-pro selected over OpenAI for cost and performance
2. **News Strategy**: RSS feeds prioritized over paid News API for reliability and coverage
3. **Database**: PostgreSQL chosen for production persistence and scalability
4. **Asset Input**: Free-text input preferred over restrictive dropdowns
5. **News Coverage**: Minimum 9 articles per analysis for comprehensive coverage

## Key Features Implemented
- ✅ Multi-agent AI analysis (Bullish, Bearish, Risk, Trader agents)
- ✅ Real-time progress tracking with WebSocket updates
- ✅ PostgreSQL database with full persistence
- ✅ RSS feed integration with 8+ professional sources
- ✅ Free-text asset input supporting any financial instrument  
- ✅ Comprehensive news filtering and relevance scoring
- ✅ Professional UI with shadcn components
- ✅ Responsive design for desktop and mobile

## Current System Performance
- **News Collection**: 9+ articles per analysis from RSS feeds
- **Analysis Speed**: 30-60 seconds for complete multi-agent analysis
- **Database**: All operations persistent with proper relationships
- **RSS Feeds**: Successfully parsing ForexLive, ActionForex, Currency Thoughts
- **Asset Support**: Handles forex pairs, Gold, Oil, BTC, stocks seamlessly

## Deployment Configuration
- **Environment**: Replit with Node.js runtime
- **Database**: Built-in PostgreSQL database
- **Port**: Application serves on port 5000
- **Secrets**: GEMINI_API_KEY, DATABASE_URL, NEWS_API_KEY configured
- **Workflow**: `npm run dev` starts both frontend and backend

## Workflow Status
- **Start application**: Running successfully on port 5000
- **Database**: Connected and operational  
- **RSS Feeds**: Active and pulling real-time news
- **AI Agents**: Processing analyses with Gemini-2.5-pro
- **Frontend**: React app with real-time updates working

## Development Notes
- RSS parsing handles CDATA sections and XML entities properly  
- Asset term mapping covers major categories (forex, commodities, crypto, stocks)
- News relevance filtering improved for different asset types
- Database schema supports longer asset names (20 chars vs 10)
- Error handling in place for RSS feed failures with graceful fallbacks

## Next Steps Mentioned by User
- Project documentation completed with comprehensive README.md
- System ready for production use with all core features implemented
- Multi-asset analysis working for any financial instrument
- RSS integration providing authentic news coverage