import { pgTable, text, serial, integer, boolean, timestamp, jsonb } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const analyses = pgTable("analyses", {
  id: serial("id").primaryKey(),
  currencyPair: text("currency_pair").notNull(),
  eventCategory: text("event_category").notNull(),
  searchQuery: text("search_query").notNull(),
  timeRange: text("time_range").notNull(),
  newsSources: text("news_sources").notNull(),
  analysisDepth: text("analysis_depth").notNull(),
  status: text("status").notNull().default("pending"), // pending, processing, completed, failed
  createdAt: timestamp("created_at").defaultNow().notNull(),
  completedAt: timestamp("completed_at"),
});

export const reports = pgTable("reports", {
  id: serial("id").primaryKey(),
  analysisId: integer("analysis_id").references(() => analyses.id).notNull(),
  agentType: text("agent_type").notNull(), // bullish, bearish, risk, trader
  content: text("content").notNull(),
  metadata: jsonb("metadata"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const newsArticles = pgTable("news_articles", {
  id: serial("id").primaryKey(),
  analysisId: integer("analysis_id").references(() => analyses.id).notNull(),
  title: text("title").notNull(),
  content: text("content").notNull(),
  source: text("source").notNull(),
  publishedAt: timestamp("published_at").notNull(),
  url: text("url").notNull(),
  sentiment: text("sentiment"), // bullish, bearish, neutral
  impact: text("impact"), // high, medium, low
});

export const agentStatuses = pgTable("agent_statuses", {
  id: serial("id").primaryKey(),
  analysisId: integer("analysis_id").references(() => analyses.id).notNull(),
  agentType: text("agent_type").notNull(),
  status: text("status").notNull(), // waiting, processing, completed, failed
  progress: integer("progress").default(0),
  message: text("message"),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const insertAnalysisSchema = createInsertSchema(analyses).pick({
  currencyPair: true,
  eventCategory: true,
  searchQuery: true,
  timeRange: true,
  newsSources: true,
  analysisDepth: true,
});

export const insertReportSchema = createInsertSchema(reports).pick({
  analysisId: true,
  agentType: true,
  content: true,
  metadata: true,
});

export const insertNewsArticleSchema = createInsertSchema(newsArticles).pick({
  analysisId: true,
  title: true,
  content: true,
  source: true,
  publishedAt: true,
  url: true,
  sentiment: true,
  impact: true,
});

export const insertAgentStatusSchema = createInsertSchema(agentStatuses).pick({
  analysisId: true,
  agentType: true,
  status: true,
  progress: true,
  message: true,
});

export type InsertAnalysis = z.infer<typeof insertAnalysisSchema>;
export type Analysis = typeof analyses.$inferSelect;
export type InsertReport = z.infer<typeof insertReportSchema>;
export type Report = typeof reports.$inferSelect;
export type InsertNewsArticle = z.infer<typeof insertNewsArticleSchema>;
export type NewsArticle = typeof newsArticles.$inferSelect;
export type InsertAgentStatus = z.infer<typeof insertAgentStatusSchema>;
export type AgentStatus = typeof agentStatuses.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});
